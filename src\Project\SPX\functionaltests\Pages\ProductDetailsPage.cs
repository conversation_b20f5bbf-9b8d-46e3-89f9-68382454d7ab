﻿#pragma warning disable 649

namespace Pmi.Spx.Project.Functional.Tests.Pages
{
    using OpenQA.Selenium;
    using OpenQA.Selenium.Support.UI;
    using Pmi.Spx.Project.Functional.Tests.Models;
    using Pmi.Web.Ui.Framework.Extensions;
    using Pmi.Web.Ui.Framework.Page;
    using System.Collections.Generic;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using FluentAssertions;
    using OpenQA.Selenium.Interactions;
    using OpenQA.Selenium.Support.Extensions;
    using System.Linq;

    public class ProductDetailsPage : BasePage<ProductDetailsPage>
    {
        private IWebElement AddToCartButton => WebDriver.FindElement(By.XPath("//span[contains(text(),'Cart')]/.. | //button[contains(text(), 'Cart')]"));

        private readonly string CartModalXPath = "//div[@class='modal-content']";
        private IWebElement ContinueToCartButton => WebDriver.FindElement(By.XPath($"{CartModalXPath}//button[contains(@class,'btn-primary')]"));

        private IList<IWebElement> MemberPrice => WebDriver.FindElements(By.XPath(".//div[@class='price-container']//span[@class='price--member__value']"));

        private IWebElement ModalTitle => WebDriver.FindElement(By.XPath(".//a[@class='basic-info__title basic-info__title-link']"));

        private IList<IWebElement> RegularPrice => WebDriver.FindElements(By.XPath("(//div[@class='price-container']//div[contains(@class, 'price--regular')]/span)[2]"));

        private IWebElement OldTitle => WebDriver.FindElement(By.XPath("//div[@id='main-content-inner']//h1"));

        private IWebElement Title => WebDriver.FindElement(By.XPath("//div[contains(@adoberegion,'pdp-hero')]/../h1"));

        private By ProductInPDPXPath => By.XPath("//div[@id='spx-pdp-hero']//div[contains(@class,'items-start')]");

        private IWebElement ProductInPDPComponent => WebDriver.FindElement(By.XPath("//div[@class = 'product-section product-section--course-details']"));

        private IList<IWebElement> RecommendedProductsInPDPPage => WebDriver.FindElements(By.XPath("//section[@class = 'recommended-products']//div[@data-index]"));

        private IWebElement Breadcrumb => WebDriver.FindElement(By.XPath("//nav[@adoberegion='pdp-bread-crumbs']//a"));

        private IWebElement YouMayAlsoLike => WebDriver.FindElement(By.XPath("//div[@id='you-may-also-like']"));

        private IWebElement RecommendedProducts => WebDriver.FindElement(By.XPath("//div[@id='you-may-also-like']//div[@aria-label='carousel']"));

        private IWebElement AlreadyInCart => WebDriver.FindElement(By.XPath("//span[text()='Already in cart']"));

        private IWebElement AddToCartButtonDisabled => WebDriver.FindElement(By.XPath("//button[@disabled][text()='Add To Cart']"));

        private IWebElement CTAButtonEnabled => WebDriver.FindElement(By.XPath("//button[text()='Continue Course'] | //button[contains(text(), 'Cart')]"));

        private IWebElement ButtonOnTheOverlay => WebDriver.FindElement(By.XPath("//div[contains(@class,'grid grid-cols-12')]//button"));

        private IList<IWebElement> PriceLockUp => WebDriver.FindElements(By.XPath("//h2[contains(text(),'Price')]/following-sibling::span"));

        private IList<IWebElement> StickyHeaderPriceLockUp => WebDriver.FindElements(By.XPath("//div[@id='sticky-header']//h2[contains(text(),'Price')]/following-sibling::span"));

        private bool AlreadyInCartDisplayed() => Extensions.CatchUnavailableElement(() => AlreadyInCart.Displayed, false);

        private bool CTAButtonEnabledDisplayed() => Extensions.CatchUnavailableElement(() => CTAButtonEnabled.Displayed, false);

        private IWebElement SkipToMainContent => WebDriver.FindElement(By.XPath("//a[contains(text(),'Skip to main content')]"));

        private readonly WebDriverWait _wait;

        private readonly UserSettings _userSettings;

        public ProductDetailsPage(IWebDriver driver) : base(driver)
        {
            _userSettings = new UserSettings();
            _wait = new WebDriverWait(WebDriver, _userSettings.DefaultExplicitWaitTimeout);
        }

        public override string BaseUrl => _userSettings.BaseUrl;

        public override string RelativePath => "/shop/";

        private bool TitleDisplayed() => Extensions.CatchUnavailableElement(() => Title.Displayed, false);

        private bool OldTitleDisplayed() => Extensions.CatchUnavailableElement(() => OldTitle.Displayed, false);

        private bool ProductInPDPDisplayed() => Extensions.CatchUnavailableElement(() => ProductInPDPComponent.Displayed, false);

        private bool AddToCartButtonDisplayed() => Extensions.CatchUnavailableElement(() => AddToCartButton.Displayed, false);

        private bool ContinueToCartButtonDisplayed() => Extensions.CatchUnavailableElement(() => ContinueToCartButton.Displayed, false);

        public override ProductDetailsPage VerifyPageLoaded()
        {
            WebDriver.WaitForAllLoadersInvisible();
            _wait.Until(_ => TitleDisplayed() | OldTitleDisplayed());
            return base.VerifyPageLoaded();
        }

        public ProductDetailsPage AddToCart()
        {
            GetCartItemDetails();
            if(!OldTitleDisplayed())
            {
                WebDriver.InvokeStickyNavigationOnPDP(_wait);
                ButtonOnTheOverlay.Click();
            }
            else
            {
                AddToCartButton.ScrollAndClickAndWait(WebDriver);
                _wait.Until(_ => ContinueToCartButtonDisplayed());
                ContinueToCartButton.ScrollAndClickAndWait(WebDriver);
            }

                return this;
        }

        private bool RegularPriceDisplayed() => Extensions.CatchUnavailableElement(() => RegularPrice.Count > 0, false);

        private bool MemberPriceDisplayed() => Extensions.CatchUnavailableElement(() => MemberPrice.Count > 0, false);

        public Product GetCartItemDetails()
        {
            var product = new Product
            {
                Title = TitleDisplayed()? Title.Text.Trim() : OldTitle.Text.Trim(),
            };

            if(RegularPriceDisplayed())
                product.RegularPrice = Currency.ParseAmount(RegularPrice[0].Text);

            if(MemberPriceDisplayed())
                product.MemberPrice = Currency.ParseAmount(MemberPrice[0].Text);

            return product;
        }

        private bool BreadcrumbDisplayed() => Extensions.CatchUnavailableElement(() => Breadcrumb.Displayed, false);
        private bool RecommendedProductsDisplayed() => Extensions.CatchUnavailableElement(() => RecommendedProducts.Displayed, false);

        public ProductDetailsPage VerifyProductTypeInBreadcrumb()
        {
            _wait.Until(_ => BreadcrumbDisplayed());
            var pdpProductType = Breadcrumb.Text.Trim();
            Breadcrumb.ScrollAndClickAndWait(WebDriver);

            var productsPage = new ProductsPage(WebDriver, pdpProductType);
            productsPage.VerifyProductType();

            return this;
        }

        public ProductDetailsPage VerifyYouMayAlsoLike()
        {
            YouMayAlsoLike.ScrollToElement(WebDriver);

            Assert.IsTrue(RecommendedProductsDisplayed(), "Recommended product is not displayed.");
            
            return this;
        }
        public ProductDetailsPage VerifyAlreadyInCart()
        {
            _wait.Until(_ => AlreadyInCartDisplayed());
            AddToCartButtonDisabled.Displayed.Should().BeTrue();
            return this;
        }

        public ProductDetailsPage VerifyAlreadyInCartNotDisplayed()
        {
            _wait.Until(_ => !AlreadyInCartDisplayed());
            _wait.Until(_ => CTAButtonEnabledDisplayed());
            return this;
        }

        public ProductDetailsPage VerifyScrollPositionAfterSkipToMainContent()
        {
            var actions = new Actions(WebDriver);
            actions.SendKeys(Keys.Tab).SendKeys(Keys.Tab).Perform();

            var PositionBeforePageScroll = WebDriver.ExecuteJavaScript<long>("return window.pageYOffset;");

            SkipToMainContent.ScrollAndClick(WebDriver);

            var pixelsScrolled = 125;
            var FinalscrollPosition = WebDriver.ExecuteJavaScript<long>("return window.pageYOffset;");
            FinalscrollPosition.Should().BeGreaterThan(PositionBeforePageScroll);
            FinalscrollPosition.Should().BeGreaterThan(pixelsScrolled);

            return this;
        }

        public ProductDetailsPage VerifyPriceLockup(StoreContext storeContext)
        {

            // Verify prices in main PriceLockUp section
            Assert.IsTrue(PriceLockUp.Any(), "No price elements found under the price lockup section.");

            foreach(var priceInfoElement in PriceLockUp)
            {
                string priceText = priceInfoElement.Text.Trim();
                ValidatePriceFormat(priceText, storeContext);
            }

            WebDriver.InvokeStickyNavigationOnPDP(_wait);

            // Verify prices in sticky header section
            Assert.IsTrue(StickyHeaderPriceLockUp.Any(), "No price elements found in the sticky header price lockup section.");

            foreach(var priceInfoElement in StickyHeaderPriceLockUp)
            {
                string priceText = priceInfoElement.Text.Trim();
                ValidatePriceFormat(priceText, storeContext);
            }

            return this;
        }

        private void ValidatePriceFormat(string priceText, StoreContext storeContext)
        {
            if(storeContext.Store == Store.IndiaStore)
            {
                Assert.IsTrue(priceText.Contains("₹"), $"Expected INR price, but missing ₹: {priceText}");
                Assert.IsFalse(priceText.Contains("USD"), $"INR price should not include USD: {priceText}");
            }
            else if(storeContext.Store == Store.BrazilStore)
            {
                Assert.IsTrue(priceText.Contains("R$"), $"Expected BRL price, but missing R$: {priceText}");
                Assert.IsFalse(priceText.Contains("USD"), $"BRL price should not include USD: {priceText}");
            }
            else
            {
                Assert.IsTrue(priceText.StartsWith("USD"), $"USD price format invalid: {priceText}");
            }
        }
    }
}