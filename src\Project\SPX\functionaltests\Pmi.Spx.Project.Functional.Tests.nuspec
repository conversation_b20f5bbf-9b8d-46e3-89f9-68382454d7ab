﻿<?xml version="1.0" encoding="utf-8"?>

<package xmlns="http://schemas.microsoft.com/packaging/2011/10/nuspec.xsd">
  <metadata>
    <id>Pmi.Spx.Project.Functional.Tests</id>
<version>2025.06.12.2</version>
<title>PMI Spx functional test pages, components and tests</title>
    <authors><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON></authors>
    <owners>Project Management Institute, Inc.</owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <description>PMI SPX Functional tests can be leveraged to extend or use it in the end to end tests.</description>
    <copyright>© 2020 Project Management Institute, Inc.</copyright>
    <dependencies>
      <dependency id="Pmi.Web.Ui.Framework-Net47" version="2023.2.8.1" />
      <dependency id="FluentAssertions" version="5.10.3" />
    </dependencies>
  </metadata>
  <files>
    <file src="bin/release/Pmi.Spx.Project.Functional.Tests.dll" target="lib\Pmi.Spx.Project.Functional.Tests.dll" />
    <file src="bin/release/Pmi.Spx.Project.Functional.Tests.pdb" target="lib\Pmi.Spx.Project.Functional.Tests.pdb" />
    <file src="bin/release/UserProfile-BrazilStore.json" target="lib\UserProfile-BrazilStore.json" />
    <file src="bin/release/UserProfile-IndiaStore.json" target="lib\UserProfile-IndiaStore.json" />
    <file src="bin/release/UserProfile-MainStore.json" target="lib\UserProfile-MainStore.json" />
    <file src="bin/release/UserRegistration.json" target="lib\UserRegistration.json" />
  </files>
</package>