﻿@local @browser:Chrome-Headless @desktop
Feature: ELearnningTests
	Ensure user is able to add an course product to cart and place an order
	Background: 
	Given an empty cart

	#todo, create a test for worldpay
@MainStore
Scenario: Buy course without promo and validate Invalid Tax Error Message in Main store
   
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Add invalid address and not save for future use
	Then Verify invalid tax error message on billing address 
	When Update creditcard payment details and not save for future use
	Then Verify Place Order button is disabled
	When Add new billing address and save for future use
	Then Verify the order summary on the checkout page
	
	When Select saved payment details
	 And Place worldpay creditcard order
	Then Verify order confirmation page

@IndiaStore @Qa @Can @Smoke @Adyen
Scenario: Verify payment summary with course in India store
   
	Given a newly registered user from data scaffolding is logged in
	When Add course to the cart from product details page
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Add new billing address and not save for future use
	When Place RazorPay Credit card order for Non-Subscription product
	Then Verify the order summary on the checkout page

@IndiaStore @Prod @Smoke @Adyen
Scenario: Verify payment summary with course in India store in Prod
   
	Given the user is logged in
	 And the cart is empty
	When Add course to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Edit billing address and not save for future use
	Then Verify RazorPay Payments
	Then Verify the order summary on the checkout page

@ChinaStore @Prod @Smoke 
Scenario: Verify payment summary for donation product in China store in Prod
   
	Given a newly registered user from data scaffolding is logged in
	 And the cart is empty
	When Add donation to the cart from product details
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Input Alipay payment details

@IndiaStore @Qa @Can @Adyen
Scenario: Buy course with promo in India store

	Given a newly registered user from data scaffolding is logged in
	When Add course to the cart
	 And Apply promo code on the cart page
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Add new billing address and not save for future use
	Then Verify the order summary on the checkout page
	
	When Place RazorPay UPI for Non-Subscription product
    Then Verify order confirmation page

@IndiaStore @Qa @Can @Adyen
Scenario: Buy course with donation using one-time payment in India store
    
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Add new billing address and not save for future use
	Then Verify the order summary on the checkout page
	
	When Place RazorPay NetBanking Order for Non-Subscription product
    Then Verify order confirmation page

@IndiaStore @Qa @Can
Scenario: Buy zero price product in India store
   
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Add new billing address and not save for future use
	Then Verify the order summary on the checkout page
            
    When Place order without payment method
	Then Verify order confirmation page

@BrazilStore @Qa @Can @Smoke
Scenario: Verify payment summary with course in Brazil store
   
	Given a newly registered user from data scaffolding is logged in
	When Add course to the cart from product details page
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Add new billing address and save for future use
	Then Verify the order summary on the checkout page

@BrazilStore @Prod @Smoke
Scenario: Verify payment summary with course in Brazil store in Prod
   
	Given the user is logged in
	 And the cart is empty
	When Add course to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Edit billing address and not save for future use
	Then Verify the order summary on the checkout page

@BrazilStore @Qa @Can
Scenario: Buy course with promo in Brazil store
   
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page

	When Proceed to checkout from cart
	 And Fill out contact information
	 And Add new billing address and save for future use
	When Apply promo code on the cart page
	Then Verify the order summary on the checkout page

	When Update creditcard payment details and save for future use
	When Place worldpay creditcard order
	Then Verify order confirmation page
	When Navigate to product details page
	Then Verify Already in Cart Message Not Displayed

@BrazilStore @Qa @Can
Scenario: Buy course using one-time payment in Brazil store
   
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Add new billing address and not save for future use
	Then Verify the order summary on the checkout page

	When Update creditcard payment details and not save for future use
    When Place worldpay creditcard order
	Then Verify order confirmation page

@BrazilStore @Qa @Can
Scenario: Buy zero price product in Brazil store
   
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Add new billing address and not save for future use
	Then Verify the order summary on the checkout page

	When Place zero dollar order
	Then Verify order confirmation page

@MainStore @Qa @Can
Scenario: Validate the information icon message for Non Member in Main store

	Given a newly registered user from data scaffolding is logged in
	When Add course to the cart from product details page
	Then Verify products on the cart page

@MainStore @Qa @Can
Scenario: Validate the information icon message for Individual Member in Main store

	Given a newly registered user from data scaffolding is logged in
	When Add course to the cart from product details page
	Then Verify products on the cart page
	When Navigate to product details page
	Then Verify Already in Cart Message

@Region2Store  @Qa @Can
Scenario: Verify that the payment went through after editing the tokenized WorldPay credit card
    
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
   
	When Proceed to checkout from cart
     And Fill out contact information
     And Fill out billing address and save for future use
     And Update creditcard payment details and save for future use
    Then Verify order summary on checkout page
     
	When Place order on checkout page
    Then Verify order confirmation page
	 And Verify order details on MyPMI page
	When Empty the cart
	 And Add digital product to the cart via SKU's
	Then Verify products on the cart page
    When Proceed to checkout from cart
	 And Edit creditcard payment details and not save for future use
	 And Place order on checkout page
	Then Verify order confirmation page