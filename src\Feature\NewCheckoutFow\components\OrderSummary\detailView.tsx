import Button from '@pmi/dsm-react/dist/components/button/Button';
import Card from '@pmi/dsm-react/dist/components/card/Card';
import { isEditorActive, withEditorChromes } from '@sitecore-jss/sitecore-jss-react';
import { useIsMutating } from '@tanstack/react-query';
import { push } from 'connected-react-router'; // TODO: will remove in the future
import {
  useAddError,
  useAdobeRegion,
  useGetShoppingCartOrderSummaryDetails,
  useLinkInteractionEvent,
} from 'Feature/NewCheckoutFow/hooks';
import { addAddressKey, setBillingAddressKey } from 'Feature/NewCheckoutFow/utils';
import { ErrorCodes } from 'Foundation/Commerce/client/BackendConstants.Generated';
import { getFormatedPrice } from 'Foundation/Extensions/client';
import { Placeholder, RichText, Text } from 'Foundation/ReactJss/client/Components';
import React from 'react';
import { useDispatch } from 'react-redux'; // TODO: will remove in the future
import { OrderSummaryLineItems } from '../OrderSummaryLineItems';
import { LoadingView } from './loadingView';
import { OrderSummaryProps } from './models';

const OrderSummary: React.FC<OrderSummaryProps> = ({ fields, params, rendering }) => {
  const { isFetching, data, error } = useGetShoppingCartOrderSummaryDetails();
  const adobeRegion = useAdobeRegion(fields?.id);
  const isBillingAddressSetting = useIsMutating({ mutationKey: setBillingAddressKey(), exact: true });
  const isAddressAdding = useIsMutating({ mutationKey: addAddressKey(), exact: true });
  const triggerLinkInteractionEvent = useLinkInteractionEvent();
  const dispatch = useDispatch();
  const { addError } = useAddError();
  const currencyCode = data?.localCurrencyCode || data?.storeCurrencyCode;

  if (error) {
    const errorData = {
      code: ErrorCodes.ConnectorErrorCode,
      message: error?.response?.errors[0]?.message,
    };
    addError(errorData, true);
  }

  if (!isEditorActive() && (isFetching || isBillingAddressSetting > 0 || isAddressAdding > 0)) {
    return <LoadingView fields={fields} />;
  }

  const handleCartClick = () => {
    triggerLinkInteractionEvent({
      linkTitle: fields?.goToCartLink?.value?.text || 'View Cart',
      linkModule: adobeRegion,
      targetURL: fields?.goToCartLink?.value?.href,
    });
    dispatch(push(fields?.goToCartLink?.value?.href));
  };
  return (
    <Card overlayOnHover={false} hideBorder={false}>
      <div className="bg-blue-200 border border-blue-300 rounded-lg p-6 m-4 max-w-md mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6 mb-4">
          <h2 className="text-xl font-bold text-gray-800 mb-4">Order Summary</h2>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Subtotal:</span>
              <span className="font-semibold">$99.99</span>
            </div>
            <div className="border-t pt-3 flex justify-between items-center">
              <span className="text-lg font-bold">Total:</span>
              <span className="text-lg font-bold text-green-600">$99.99</span>
            </div>
          </div>
        </div>
      </div>
      <div className="order-summary" adoberegion={adobeRegion}>
        <div className="row">
          <div className="col col-8">
            <Text tag="span" field={fields?.title} className="order-summary__title" />
          </div>
          {params.displayCartLines && (
            <div className="col col-4 text-end">
              <Button
                titleText={fields?.goToCartLink?.value?.text ?? 'View Cart'}
                variant="tertiary"
                className="order-summary__cartbutton"
                onClick={handleCartClick}
                size="sm"
              />
            </div>
          )}
        </div>
        <hr />
        {params.displayCartLines && data.itemsQuantity > 0 && (
          <>
            <OrderSummaryLineItems adobeRegionValue={adobeRegion} cartData={data} />
            <hr />
          </>
        )}

        <div className="row order-summary__subtotal">
          <div className="col col-7">
            <RichText field={fields?.subtotal} tag="span" />
          </div>
          <div className="col col-5 text-end">
            {data?.storeCurrencySymbol}
            {getFormatedPrice(Math.abs(data?.price?.subTotal))}
          </div>
        </div>
        {data?.price?.discountAmount ? (
          <div className="row order-summary__discount-line">
            <div className="col col-7">
              <RichText field={fields?.orderDiscount} />
            </div>
            <div className="col col-5 text-end order-summary__discount-line-price">
              - {data?.storeCurrencySymbol}
              {getFormatedPrice(Math.abs(data?.price?.discountAmount))}
            </div>
          </div>
        ) : null}
        {data?.price?.voucherDiscountAmount ? (
          <div className="row order-summary__discount-line">
            <div className="col col-7">
              <RichText field={fields?.voucherOrderDiscount} />
            </div>
            <div className="col col-5 text-end order-summary__voucherdiscount-line-price">
              - {data?.storeCurrencySymbol}
              {getFormatedPrice(Math.abs(data?.price?.voucherDiscountAmount))}
            </div>
          </div>
        ) : null}
        <div className="row order-summary__taxes">
          <div className="col col-7">
            <RichText field={fields?.estimatedTax} />
          </div>
          <div className="col col-5 text-end">
            {data?.storeCurrencySymbol}
            {getFormatedPrice(Math.abs(data?.price?.taxAmount))}
          </div>
        </div>
        <hr />
        <div className="row order-summary__grand-total">
          <div className="col col-6">
            <RichText field={fields?.estimatedTotal} />
          </div>
          <div className="col col-6 text-end text-nowrap">
            {currencyCode === 'USD' ? 'USD' : null}
            {!!data?.localCurrencyCode ? data?.localCurrencySymbol : data?.storeCurrencySymbol}
            {getFormatedPrice(
              Math.abs(
                !!data?.localCurrencyCode ? data?.price?.totalGrossLocalCurrencyAmount : data?.price?.grandTotal,
              ),
            )}
          </div>
        </div>
        {!params.hideGoToCheckoutButton && (
          <div className="text-center order-summary__submit-btn">
            <Placeholder name="order-summary-submit-button" rendering={rendering} />
          </div>
        )}
        <div className="order-summary__legal">
          <Placeholder name="order-summary-terms" rendering={rendering} />
        </div>
      </div>
    </Card>
  );
};

export default withEditorChromes(OrderSummary);
