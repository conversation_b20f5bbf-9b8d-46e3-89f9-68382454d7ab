// Tailwind CSS v1.9.6 configuration
module.exports = {
  purge: {
    // Enable purging in production builds to remove unused CSS
    enabled: process.env.NODE_ENV === 'production',
    content: [
      // Scan NewCheckoutFlow components for Tailwind classes
      './src/Feature/NewCheckoutFow/**/*.{ts,tsx,js,jsx}',
      // Add other paths as needed for testing
      './src/**/*.{ts,tsx,js,jsx}',
    ],
  },
  theme: {
    extend: {
      // Add custom theme extensions here if needed
      // This allows you to extend Tailwind's default theme
      // while maintaining compatibility with your existing design system
    },
  },
  variants: {
    // Add variant extensions here if needed
    // Example: backgroundColor: ['responsive', 'hover', 'focus']
  },
  plugins: [
    // Add Tailwind plugins here if needed
  ],
  // Ensure compatibility with existing CSS
  corePlugins: {
    // Disable preflight if it conflicts with existing styles
    // preflight: false,
  },
};
