// Tailwind CSS v3.4.17 configuration
module.exports = {
  content: [
    // Scan NewCheckoutFlow components for Tailwind classes
    './src/Feature/NewCheckoutFow/**/*.{ts,tsx,js,jsx}',
    // Add other paths as needed for testing
    './src/**/*.{ts,tsx,js,jsx}',
  ],
  theme: {
    extend: {
      // Add custom theme extensions here if needed
      // This allows you to extend Tailwind's default theme
      // while maintaining compatibility with your existing design system
    },
  },
  plugins: [
    // Add Tailwind plugins here if needed
  ],
  // Ensure compatibility with existing CSS
  corePlugins: {
    // Disable preflight if it conflicts with existing styles
    // preflight: false,
  },
};
