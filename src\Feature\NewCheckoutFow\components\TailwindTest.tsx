import React from 'react';

/**
 * Test component to verify Tailwind CSS v3.4.17 is working correctly
 * This component uses various Tailwind utilities to test the upgrade
 */
export const TailwindTest: React.FC = () => {
  return (
    <div className="max-w-md mx-auto bg-white rounded-xl shadow-lg overflow-hidden md:max-w-2xl">
      <div className="md:flex">
        <div className="md:shrink-0">
          <div className="h-48 w-full object-cover md:h-full md:w-48 bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <span className="text-white font-bold text-xl">Tailwind v3.4.17</span>
          </div>
        </div>
        <div className="p-8">
          <div className="uppercase tracking-wide text-sm text-indigo-500 font-semibold">
            Upgrade Test
          </div>
          <h2 className="block mt-1 text-lg leading-tight font-medium text-black hover:underline">
            Tailwind CSS Successfully Upgraded!
          </h2>
          <p className="mt-2 text-slate-500">
            This component demonstrates that Tailwind CSS v3.4.17 is working correctly with:
          </p>
          <ul className="mt-4 space-y-2">
            <li className="flex items-center text-sm text-gray-600">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
              Responsive design utilities (md:flex, md:shrink-0)
            </li>
            <li className="flex items-center text-sm text-gray-600">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
              Modern color palette (slate-500, indigo-500)
            </li>
            <li className="flex items-center text-sm text-gray-600">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
              Gradient utilities (bg-gradient-to-r)
            </li>
            <li className="flex items-center text-sm text-gray-600">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
              Flexbox and spacing utilities
            </li>
          </ul>
          <div className="mt-6">
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200">
              Test Button
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TailwindTest;
